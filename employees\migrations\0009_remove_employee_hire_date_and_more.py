# Generated by Django 5.2 on 2025-07-25 12:15

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0008_merge_0004_employeedetails_0007_internaltransfer'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='employee',
            name='hire_date',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='masters_degree',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='phd_degree',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='post_graduate_diploma',
        ),
        migrations.RemoveField(
            model_name='employee',
            name='school',
        ),
        migrations.AddField(
            model_name='employee',
            name='blood_type',
            field=models.CharField(blank=True, max_length=5, null=True, verbose_name='Blood Type'),
        ),
        migrations.AddField(
            model_name='employee',
            name='education_level',
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True, verbose_name='Education Level'),
        ),
        migrations.AddField(
            model_name='employee',
            name='email',
            field=models.EmailField(default='<EMAIL>', max_length=254, verbose_name='Email'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_contact',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Emergency Contact'),
        ),
        migrations.AddField(
            model_name='employee',
            name='emergency_phone',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Emergency Phone'),
        ),
        migrations.AddField(
            model_name='employee',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is Active'),
        ),
        migrations.AddField(
            model_name='employee',
            name='marital_status',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='Marital Status'),
        ),
        migrations.AddField(
            model_name='employee',
            name='nationality',
            field=models.CharField(default='عراقي', max_length=50, verbose_name='Nationality'),
        ),
        migrations.AddField(
            model_name='employee',
            name='religion',
            field=models.CharField(blank=True, max_length=50, null=True, verbose_name='Religion'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='ministry_number',
            field=models.CharField(max_length=50, unique=True, verbose_name='Ministry Number'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='qualification',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Qualification'),
        ),
        migrations.AlterField(
            model_name='employee',
            name='specialization',
            field=models.CharField(blank=True, max_length=255, null=True, verbose_name='Specialization'),
        ),
    ]
