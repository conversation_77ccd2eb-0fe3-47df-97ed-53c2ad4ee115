# Generated manually for EmployeeEducation model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0002_employee_gender_alter_employee_school'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeEducation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qualification', models.CharField(blank=True, max_length=255, null=True, verbose_name='المؤهل العام')),
                ('post_graduate_diploma', models.CharField(blank=True, max_length=255, null=True, verbose_name='دبلوم عالي')),
                ('masters_degree', models.CharField(blank=True, max_length=255, null=True, verbose_name='ماجستير')),
                ('phd_degree', models.CharField(blank=True, max_length=255, null=True, verbose_name='دكتوراه')),
                ('doctorate_degree', models.Char<PERSON>ield(blank=True, max_length=255, null=True, verbose_name='دكتوراه')),
                ('professional_degree', models.CharField(blank=True, max_length=255, null=True, verbose_name='شهادة مهنية')),
                ('associate_degree', models.CharField(blank=True, max_length=255, null=True, verbose_name='دبلوم متوسط')),
                ('bachelor_degree', models.CharField(blank=True, max_length=255, null=True, verbose_name='بكالوريوس')),
                ('high_school_diploma', models.CharField(blank=True, max_length=255, null=True, verbose_name='الثانوية العامة')),
                ('vocational_training', models.CharField(blank=True, max_length=255, null=True, verbose_name='تدريب مهني')),
                ('technical_certification', models.CharField(blank=True, max_length=255, null=True, verbose_name='شهادة تقنية')),
                ('trade_school', models.CharField(blank=True, max_length=255, null=True, verbose_name='مدرسة حرفية')),
                ('apprenticeship', models.CharField(blank=True, max_length=255, null=True, verbose_name='تدريب مهني')),
                ('degree', models.CharField(blank=True, max_length=100, null=True, verbose_name='الدرجة')),
                ('university', models.CharField(blank=True, max_length=255, null=True, verbose_name='الجامعة')),
                ('graduation_year', models.IntegerField(blank=True, null=True, verbose_name='سنة التخرج')),
                ('gpa', models.DecimalField(blank=True, decimal_places=2, max_digits=3, null=True, verbose_name='المعدل')),
                ('major', models.CharField(blank=True, max_length=255, null=True, verbose_name='التخصص الرئيسي')),
                ('minor', models.CharField(blank=True, max_length=255, null=True, verbose_name='التخصص الفرعي')),
                ('thesis_title', models.TextField(blank=True, null=True, verbose_name='عنوان الأطروحة')),
                ('academic_honors', models.TextField(blank=True, null=True, verbose_name='الأوسمة الأكاديمية')),
                ('publications', models.TextField(blank=True, null=True, verbose_name='المنشورات')),
                ('research_experience', models.TextField(blank=True, null=True, verbose_name='الخبرة البحثية')),
                ('teaching_experience', models.TextField(blank=True, null=True, verbose_name='الخبرة التدريسية')),
                ('professional_licenses', models.TextField(blank=True, null=True, verbose_name='التراخيص المهنية')),
                ('license_numbers', models.TextField(blank=True, null=True, verbose_name='أرقام التراخيص')),
                ('license_expiry_dates', models.TextField(blank=True, null=True, verbose_name='تواريخ انتهاء التراخيص')),
                ('continuing_education', models.TextField(blank=True, null=True, verbose_name='التعليم المستمر')),
                ('online_courses_completed', models.TextField(blank=True, null=True, verbose_name='الدورات الإلكترونية')),
                ('moocs_completed', models.TextField(blank=True, null=True, verbose_name='دورات MOOC')),
                ('bootcamp_completed', models.CharField(blank=True, max_length=255, null=True, verbose_name='معسكر تدريبي')),
                ('coding_bootcamp', models.CharField(blank=True, max_length=255, null=True, verbose_name='معسكر برمجة')),
                ('data_science_bootcamp', models.CharField(blank=True, max_length=255, null=True, verbose_name='معسكر علوم البيانات')),
                ('digital_marketing_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة تسويق رقمي')),
                ('project_management_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة إدارة مشاريع')),
                ('leadership_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة قيادة')),
                ('management_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة إدارة')),
                ('finance_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة مالية')),
                ('accounting_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة محاسبة')),
                ('hr_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة موارد بشرية')),
                ('marketing_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة تسويق')),
                ('sales_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة مبيعات')),
                ('customer_service_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة خدمة عملاء')),
                ('communication_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة تواصل')),
                ('presentation_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة عرض')),
                ('negotiation_course', models.CharField(blank=True, max_length=255, null=True, verbose_name='دورة تفاوض')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='education_records', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'مؤهل تعليمي',
                'verbose_name_plural': 'المؤهلات التعليمية',
                'db_table': 'employees_education',
                'ordering': ['-created_at'],
            },
        ),
    ]
