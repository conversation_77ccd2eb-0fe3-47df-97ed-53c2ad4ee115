# Generated manually for EmployeeDetails model

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('employees', '0003_employeeeducation'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmployeeDetails',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('first_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم الأول')),
                ('last_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='الاسم الأخير')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('personal_email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الشخصي')),
                ('secondary_phone', models.Char<PERSON>ield(blank=True, max_length=20, null=True, verbose_name='هاتف ثانوي')),
                ('whatsapp_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم واتساب')),
                ('hire_date', models.DateField(blank=True, null=True, verbose_name='تاريخ التوظيف')),
                ('salary', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='الراتب')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='المنصب')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='القسم')),
                ('manager_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم المدير')),
                ('work_schedule', models.CharField(blank=True, max_length=100, null=True, verbose_name='جدول العمل')),
                ('employment_type', models.CharField(blank=True, max_length=50, null=True, verbose_name='نوع التوظيف')),
                ('street_address', models.CharField(blank=True, max_length=255, null=True, verbose_name='عنوان الشارع')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('state_province', models.CharField(blank=True, max_length=100, null=True, verbose_name='المحافظة')),
                ('postal_code', models.CharField(blank=True, max_length=20, null=True, verbose_name='الرمز البريدي')),
                ('country', models.CharField(default='العراق', max_length=100, verbose_name='البلد')),
                ('birth_place', models.CharField(blank=True, max_length=255, null=True, verbose_name='مكان الولادة')),
                ('passport_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الجواز')),
                ('passport_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء الجواز')),
                ('driving_license', models.CharField(blank=True, max_length=50, null=True, verbose_name='رخصة القيادة')),
                ('license_expiry', models.DateField(blank=True, null=True, verbose_name='انتهاء الرخصة')),
                ('bank_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم البنك')),
                ('bank_account', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم الحساب')),
                ('iban', models.CharField(blank=True, max_length=50, null=True, verbose_name='IBAN')),
                ('tax_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='الرقم الضريبي')),
                ('social_security', models.CharField(blank=True, max_length=50, null=True, verbose_name='الضمان الاجتماعي')),
                ('emergency_contact_2', models.CharField(blank=True, max_length=255, null=True, verbose_name='جهة اتصال طوارئ 2')),
                ('emergency_phone_2', models.CharField(blank=True, max_length=20, null=True, verbose_name='هاتف طوارئ 2')),
                ('emergency_relationship', models.CharField(blank=True, max_length=100, null=True, verbose_name='صلة القرابة')),
                ('medical_conditions', models.TextField(blank=True, null=True, verbose_name='الحالات الطبية')),
                ('allergies', models.TextField(blank=True, null=True, verbose_name='الحساسية')),
                ('spouse_name', models.CharField(blank=True, max_length=255, null=True, verbose_name='اسم الزوج/الزوجة')),
                ('spouse_occupation', models.CharField(blank=True, max_length=255, null=True, verbose_name='مهنة الزوج/الزوجة')),
                ('children_count', models.IntegerField(default=0, verbose_name='عدد الأطفال')),
                ('dependents_count', models.IntegerField(default=0, verbose_name='عدد المعالين')),
                ('skills', models.TextField(blank=True, null=True, verbose_name='المهارات')),
                ('languages', models.TextField(blank=True, null=True, verbose_name='اللغات')),
                ('certifications', models.TextField(blank=True, null=True, verbose_name='الشهادات')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات')),
                ('hobbies', models.TextField(blank=True, null=True, verbose_name='الهوايات')),
                ('work_location', models.CharField(blank=True, max_length=255, null=True, verbose_name='مكان العمل')),
                ('office_number', models.CharField(blank=True, max_length=50, null=True, verbose_name='رقم المكتب')),
                ('extension_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم التحويلة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('employee', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='details', to='employees.employee', verbose_name='الموظف')),
            ],
            options={
                'verbose_name': 'تفاصيل الموظف',
                'verbose_name_plural': 'تفاصيل الموظفين',
                'db_table': 'employees_details',
                'ordering': ['-created_at'],
            },
        ),
    ]
