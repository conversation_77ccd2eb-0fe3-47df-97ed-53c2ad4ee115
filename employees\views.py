from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.http import HttpResponse, JsonResponse
from django.db.models import Q
from django.utils import timezone
from django.core.paginator import Paginator
from django.template.loader import render_to_string
import pandas as pd
from io import BytesIO
from datetime import date, datetime
# ReportLab imports
from reportlab.lib.pagesizes import A4, landscape
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os
# Try to import Arabic text processing libraries
try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False
    # Create dummy functions for when libraries are not available
    def arabic_reshaper_reshape(text):
        return text

    def get_display(text):
        return text
from openpyxl.styles import Font, PatternFill, Border, Side
from .models import Employee, AnnualReport, Penalty, RetiredEmployee, ExternalTransfer, InternalTransfer, EmployeeEducation, EmployeeDetails
from .forms import EmployeeForm, AnnualReportForm, PenaltyForm, EmployeeImportForm, RetiredEmployeeForm, RetireEmployeeForm
from employment.models import Department, Position, AppointmentType
from leaves.models import Leave, LeaveType
from accounts.models import User
from ranks.models import RankType, EmployeeRank
# FileMovement استيراد محذوف - نستخدم raw SQL بدلاً منه
from system_logs.models import SystemLog

def safe_get_employee_field(employee, field_name, default=""):
    """دالة مساعدة للحصول على حقول الموظف بأمان"""
    # استخدام الدوال المباشرة بدلاً من الخصائص
    if field_name == 'post_graduate_diploma':
        return employee.get_post_graduate_diploma_from_education_table()
    elif field_name == 'qualification':
        return employee.get_qualification_from_education_table()
    elif field_name == 'school':
        return employee.get_school_from_education_table()
    elif field_name == 'first_name':
        return employee.get_first_name_from_details_table()
    elif field_name == 'last_name':
        return employee.get_last_name_from_details_table()
    elif field_name == 'phone':
        return employee.get_phone_from_details_table()
    elif field_name == 'salary':
        return employee.get_salary_from_details_table()
    else:
        try:
            return getattr(employee, field_name, default)
        except:
            return default

@login_required
def dashboard(request):
    # Get counts for dashboard
    employee_count = Employee.objects.count()
    department_count = Department.objects.count()
    user_count = User.objects.count()

    # Get active leaves
    try:
        from leaves.models import Leave
        today = timezone.now().date()
        active_leaves_count = Leave.objects.filter(
            start_date__lte=today,
            end_date__gte=today,
            status='approved'
        ).count()
    except (ImportError, Exception):
        active_leaves_count = 0

    # Get counts for new sections
    rank_count = EmployeeRank.objects.count()

    # Get file count from database directly
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM file_management_filemovement")
            file_count = cursor.fetchone()[0]
    except Exception:
        file_count = 0

    # Get unpaid leave count
    try:
        from leaves.models import Leave, LeaveType
        unpaid_leave_type = LeaveType.objects.filter(name=LeaveType.UNPAID).first()
        if unpaid_leave_type:
            unpaid_leave_count = Leave.objects.filter(leave_type=unpaid_leave_type).count()
        else:
            unpaid_leave_count = 0
    except (ImportError, Exception):
        unpaid_leave_count = 0

    system_log_count = SystemLog.objects.exclude(action=SystemLog.VIEW).count()

    # Get leave types using safe query
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT COUNT(*) FROM leaves_leavetype")
            leave_types_count = cursor.fetchone()[0]

            # إنشاء كائنات مؤقتة للعرض
            leave_types = []
            for i in range(min(5, leave_types_count)):
                leave_type = type('LeaveType', (), {
                    'id': i + 1,
                    'name': f'نوع إجازة {i + 1}',
                    'description': 'وصف الإجازة'
                })()
                leave_types.append(leave_type)
    except Exception as e:
        print(f"Error getting leave types: {e}")
        leave_types = []

    # Get recent employees - use raw SQL to avoid field errors
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    e.id,
                    e.ministry_number,
                    e.full_name,
                    get_employee_school(e.id) as school,
                    e.created_at
                FROM employees_employee e
                ORDER BY e.created_at DESC
                LIMIT 5
            """)
            recent_employees_data = cursor.fetchall()

            # تحويل النتائج لكائنات Employee مع إضافة school
            recent_employees = []
            for row in recent_employees_data:
                emp = Employee.objects.get(id=row[0])
                emp._school = row[3]  # إضافة المدرسة كخاصية مؤقتة
                recent_employees.append(emp)
    except Exception as e:
        print(f"Error getting recent employees: {e}")
        recent_employees = []

    # Get recent leaves using safe query
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    l.id,
                    l.employee_id,
                    l.start_date,
                    l.end_date,
                    l.days_count,
                    l.status,
                    l.created_at,
                    e.full_name
                FROM leaves_leave l
                LEFT JOIN employees_employee e ON l.employee_id = e.id
                ORDER BY l.created_at DESC
                LIMIT 5
            """)
            leaves_data = cursor.fetchall()

            # تحويل النتائج لكائنات مؤقتة
            recent_leaves = []
            for row in leaves_data:
                leave = type('Leave', (), {
                    'id': row[0],
                    'employee_id': row[1],
                    'start_date': row[2],
                    'end_date': row[3],
                    'days_count': row[4],
                    'status': row[5],
                    'created_at': row[6],
                    'employee': type('Employee', (), {'full_name': row[7]})()
                })()
                recent_leaves.append(leave)
    except Exception as e:
        print(f"Error getting recent leaves: {e}")
        recent_leaves = []

    # Get recent file movements using raw SQL
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT
                    fm.id,
                    fm.employee_id,
                    fm.file_name,
                    fm.status,
                    fm.created_at,
                    e.full_name
                FROM file_management_filemovement fm
                LEFT JOIN employees_employee e ON fm.employee_id = e.id
                ORDER BY fm.created_at DESC
                LIMIT 5
            """)
            file_movements_data = cursor.fetchall()

            # تحويل النتائج لكائنات مؤقتة
            recent_file_movements = []
            for row in file_movements_data:
                movement = type('FileMovement', (), {
                    'id': row[0],
                    'employee_id': row[1],
                    'file_name': row[2],
                    'status': row[3],
                    'created_at': row[4],
                    'employee': type('Employee', (), {'full_name': row[5]})()
                })()
                recent_file_movements.append(movement)
    except Exception as e:
        print(f"Error getting file movements: {e}")
        recent_file_movements = []

    # Get recent ranks
    # Get recent ranks using raw SQL
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT 
                    er.id,
                    er.employee_id,
                    COALESCE(er.date_obtained, '2024-01-01') as date_obtained,
                    er.created_at,
                    e.full_name,
                    COALESCE(rt.name, 'غير محدد') as rank_name
                FROM ranks_employeerank er
                LEFT JOIN employees_employee e ON er.employee_id = e.id
                LEFT JOIN ranks_ranktype rt ON er.rank_type_id = rt.id
                ORDER BY er.created_at DESC
                LIMIT 5
            """)
            ranks_data = cursor.fetchall()
            
            # تحويل النتائج لكائنات مؤقتة
            recent_ranks = []
            for row in ranks_data:
                rank = type('EmployeeRank', (), {
                    'id': row[0],
                    'employee_id': row[1],
                    'date_obtained': row[2],
                    'created_at': row[3],
                    'employee': type('Employee', (), {'full_name': row[4]})(),
                    'rank_type': type('RankType', (), {'name': row[5]})()
                })()
                recent_ranks.append(rank)
    except Exception as e:
        print(f"Error getting recent ranks: {e}")
        recent_ranks = []

    return render(request, 'dashboard.html', {
        'employee_count': employee_count,
        'department_count': department_count,
        'user_count': user_count,
        'active_leaves_count': active_leaves_count,
        'rank_count': rank_count,
        'file_count': file_count,
        'unpaid_leave_count': unpaid_leave_count,
        'system_log_count': system_log_count,
        'leave_types': leave_types,
        'recent_employees': recent_employees,
        'recent_leaves': recent_leaves,
        'recent_file_movements': recent_file_movements,
        'recent_ranks': recent_ranks,
    })

@login_required
def calculate_age(request):
    """View for calculating employee ages"""
    # Get all employees with birth_date
    employees = Employee.objects.filter(birth_date__isnull=False).order_by('full_name')

    # Calculate age for each employee
    today = date.today()
    for employee in employees:
        if employee.birth_date:
            born = employee.birth_date
            # Calculate years
            years = today.year - born.year - ((today.month, today.day) < (born.month, born.day))

            # Calculate months and days
            if today.day >= born.day:  # Same day or later in the month
                days = today.day - born.day
                if today.month >= born.month:  # Same or later month
                    months = today.month - born.month
                else:  # Earlier month, wrapped around from previous year
                    months = today.month + 12 - born.month
            else:  # Earlier day, wrapped around from previous month
                # Get days in the previous month
                if today.month == 1:  # January
                    prev_month = 12  # December of previous year
                    prev_month_year = today.year - 1
                else:
                    prev_month = today.month - 1
                    prev_month_year = today.year

                import calendar
                last_day_of_prev_month = calendar.monthrange(prev_month_year, prev_month)[1]
                days = today.day + last_day_of_prev_month - born.day

                if today.month > born.month:  # Later month in same year
                    months = today.month - 1 - born.month
                elif today.month == born.month:  # Same month
                    months = 11  # 11 months since last birthday
                else:  # Earlier month, wrapped from previous year
                    months = today.month + 11 - born.month

            # Store age components
            employee.age_years = years
            employee.age_months = months
            employee.age_days = days
        else:
            employee.age_years = None
            employee.age_months = None
            employee.age_days = None

    # Get current employment for each employee
    for employee in employees:
        employment = employee.employments.filter(is_current=True).first()
        if employment:
            employee.position_name = employment.position.name
        else:
            employee.position_name = '-'

    return render(request, 'employees/calculate_age.html', {
        'employees': employees,
        'today': today
    })

@login_required
def employee_list(request):
    search_query = request.GET.get('search', '')

    # Get filter parameters
    specialization_filter = request.GET.get('specialization', '')
    appointment_type_filter = request.GET.get('appointment_type', '')
    department_filter = request.GET.get('department', '')
    position_filter = request.GET.get('position', '')
    gender_filter = request.GET.get('gender', '')

    # Start with all employees excluding retired ones, externally transferred ones, and service purchase employees
    retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
    transferred_employee_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
    
    # Also exclude employees with active service purchase
    try:
        from employment.models import ServicePurchase
        service_purchase_employee_ids = ServicePurchase.objects.filter(is_active=True).values_list('employee_id', flat=True)
    except ImportError:
        service_purchase_employee_ids = []
    
    # Combine all exclusions
    excluded_ids = list(retired_employee_ids) + list(transferred_employee_ids) + list(service_purchase_employee_ids)
    employees = Employee.objects.exclude(id__in=excluded_ids)

    # Apply search filter using safe fields only
    if search_query:
        employees = employees.filter(
            Q(ministry_number__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Apply gender filter (safe field)
    if gender_filter:
        employees = employees.filter(gender=gender_filter)

    # Skip specialization and department filters for now as they require education table joins

    # Apply appointment type filter
    if appointment_type_filter:
        try:
            appointment_type_id = int(appointment_type_filter)
            employees = employees.filter(
                employments__appointment_type__id=appointment_type_id,
                employments__is_current=True
            )
        except (ValueError, TypeError):
            pass

    # Apply position filter
    if position_filter:
        try:
            position_id = int(position_filter)
            employees = employees.filter(
                employments__position__id=position_id,
                employments__is_current=True
            )
        except (ValueError, TypeError):
            pass

    # Remove duplicates that might occur due to joins
    employees = employees.distinct()
    
    # Order by ministry number ascending (numeric sorting)
    from .utils import order_employees_by_ministry_number
    employees = order_employees_by_ministry_number(employees)
    
    # Store original count before potential list conversion
    original_count = len(employees) if isinstance(employees, list) else employees.count()

    # Get departments, positions and appointment types for the form and filters
    from employment.models import Department, Position, AppointmentType
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')
    appointment_types = AppointmentType.objects.all().order_by('name')

    # Get unique specializations and departments from education table using raw SQL
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            # Get specializations from education table
            cursor.execute("SELECT DISTINCT major FROM employees_education WHERE major IS NOT NULL AND major != '' ORDER BY major")
            specializations = [row[0] for row in cursor.fetchall()]

            # Get departments (schools) from education table
            cursor.execute("SELECT DISTINCT COALESCE(school, university) FROM employees_education WHERE COALESCE(school, university) IS NOT NULL AND COALESCE(school, university) != '' ORDER BY COALESCE(school, university)")
            unique_departments = [row[0] for row in cursor.fetchall()]
    except Exception as e:
        print(f"Error getting specializations/departments: {e}")
        specializations = []
        unique_departments = []

    # Create a form instance for the modal
    form = EmployeeForm()

    # Handle POST requests from the modal form
    if request.method == 'POST':
        form = EmployeeForm(request.POST)

        # Check if ministry number already exists
        ministry_number = request.POST.get('ministry_number')
        if ministry_number and Employee.objects.filter(ministry_number=ministry_number).exists():
            messages.error(request, 'الرقم الوزاري موجود بالفعل. الرجاء استخدام رقم وزاري آخر.')
            return render(request, 'employees/employee_data.html', {
                'employees': employees,
                'search_query': search_query,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'specializations': specializations,
                'unique_departments': unique_departments,
                'form': form,
                # Filter values to maintain state
                'specialization_filter': specialization_filter,
                'appointment_type_filter': appointment_type_filter,
                'department_filter': department_filter,
                'position_filter': position_filter,
                'gender_filter': gender_filter,
                'total_employees': original_count,
            })

        # Check if national ID already exists
        national_id = request.POST.get('national_id')
        if national_id and Employee.objects.filter(national_id=national_id).exists():
            messages.error(request, 'الرقم الوطني موجود بالفعل. الرجاء استخدام رقم وطني آخر.')
            return render(request, 'employees/employee_data.html', {
                'employees': employees,
                'search_query': search_query,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'specializations': specializations,
                'unique_departments': unique_departments,
                'form': form,
                # Filter values to maintain state
                'specialization_filter': specialization_filter,
                'appointment_type_filter': appointment_type_filter,
                'department_filter': department_filter,
                'position_filter': position_filter,
                'gender_filter': gender_filter,
                'total_employees': original_count,
            })

        if form.is_valid():
            employee = form.save()

            # Handle position if provided
            position_id = request.POST.get('position')
            if position_id:
                from employment.models import Employment, EmploymentStatus, EmployeePosition
                position = Position.objects.get(id=position_id)

                # Get or create a default department
                department, _ = Department.objects.get_or_create(
                    name='قسم عام',
                    defaults={'description': 'قسم افتراضي للموظفين الجدد'}
                )

                # Get or create a default employment status
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )

                # Get appointment type if provided
                appointment_type_id = request.POST.get('appointment_type')
                appointment_type = None
                if appointment_type_id:
                    appointment_type = AppointmentType.objects.get(id=appointment_type_id)

                # Create employment record
                Employment.objects.create(
                    employee=employee,
                    position=position,
                    department=department,
                    status=status,
                    appointment_type=appointment_type,
                    start_date=employee.hire_date,
                    is_current=True
                )

                # Also create an EmployeePosition record to track position history
                EmployeePosition.objects.create(
                    employee=employee,
                    position=position,
                    date_obtained=employee.hire_date,
                    notes="تم إنشاء المسمى الوظيفي عند إضافة الموظف"
                )

            messages.success(request, 'تم إضافة الموظف بنجاح.')

            # Create notification for new employee
            try:
                from notifications.utils import notify_employee_added
                notify_employee_added(employee, request.user)
            except ImportError:
                pass  # Notifications app not available

            return redirect('employees:employee_list')

    # Add pagination
    from django.core.paginator import Paginator
    page_number = request.GET.get('page', 1)
    per_page_param = request.GET.get('per_page', 'all')

    # Handle "all" option
    if per_page_param == 'all':
        per_page = original_count  # Show all records
        page_obj = None
        paginated_employees = employees
    else:
        per_page = int(per_page_param)
        paginator = Paginator(employees, per_page)
        page_obj = paginator.get_page(page_number)
        paginated_employees = page_obj

    return render(request, 'employees/employee_data.html', {
        'employees': paginated_employees,
        'search_query': search_query,
        'departments': departments,
        'positions': positions,
        'appointment_types': appointment_types,
        'specializations': specializations,
        'unique_departments': unique_departments,
        'form': form,
        # Filter values to maintain state
        'specialization_filter': specialization_filter,
        'appointment_type_filter': appointment_type_filter,
        'department_filter': department_filter,
        'position_filter': position_filter,
        'gender_filter': gender_filter,
        'per_page': per_page_param,
        'page_obj': page_obj,
        'total_employees': original_count
    })

@login_required
def get_employees_ajax(request):
    """AJAX view for getting employees with filters"""
    try:
        search_query = request.GET.get('search', '')

        # Get filter parameters
        specialization_filter = request.GET.get('specialization', '')
        appointment_type_filter = request.GET.get('appointment_type', '')
        department_filter = request.GET.get('department', '')
        position_filter = request.GET.get('position', '')
        gender_filter = request.GET.get('gender', '')

        # Get pagination parameters
        page_number = request.GET.get('page', 1)
        per_page_param = request.GET.get('per_page', '50')

        # Start with all employees excluding retired ones, externally transferred ones, and service purchase employees
        retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_employee_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)

        # Also exclude employees with active service purchase
        try:
            from employment.models import ServicePurchase
            service_purchase_employee_ids = ServicePurchase.objects.filter(is_active=True).values_list('employee_id', flat=True)
        except ImportError:
            service_purchase_employee_ids = []

        # Combine all exclusions
        excluded_ids = list(retired_employee_ids) + list(transferred_employee_ids) + list(service_purchase_employee_ids)
        employees = Employee.objects.exclude(id__in=excluded_ids)

        # Apply search filter
        if search_query:
            employees = employees.filter(
                Q(ministry_number__icontains=search_query) |
                Q(national_id__icontains=search_query) |
                Q(full_name__icontains=search_query) |
                Q(school__icontains=search_query)
            )

        # Apply specialization filter
        if specialization_filter:
            employees = employees.filter(specialization__icontains=specialization_filter)

        # Apply gender filter
        if gender_filter:
            employees = employees.filter(gender=gender_filter)

        # Apply department filter (using school field)
        if department_filter:
            employees = employees.filter(school__icontains=department_filter)

        # Apply appointment type filter
        if appointment_type_filter:
            try:
                appointment_type_id = int(appointment_type_filter)
                employees = employees.filter(
                    employments__appointment_type__id=appointment_type_id,
                    employments__is_current=True
                )
            except (ValueError, TypeError):
                pass

        # Apply position filter
        if position_filter:
            try:
                position_id = int(position_filter)
                employees = employees.filter(
                    employments__position__id=position_id,
                    employments__is_current=True
                )
            except (ValueError, TypeError):
                pass

        # Get total count before pagination
        total_count = employees.count()

        # Handle "all" option for pagination
        if per_page_param == 'all':
            per_page = total_count if total_count > 0 else 1
            page_obj = None
            paginated_employees = employees
        else:
            per_page = int(per_page_param)
            paginator = Paginator(employees, per_page)
            page_obj = paginator.get_page(page_number)
            paginated_employees = page_obj

        # Render employees table to HTML
        employees_html = render_to_string('employees/employee_table_rows.html', {
            'employees': paginated_employees,
            'page_obj': page_obj
        })

        # Return JSON response
        return JsonResponse({
            'html': employees_html,
            'total_count': total_count,
            'has_previous': page_obj.has_previous() if page_obj else False,
            'has_next': page_obj.has_next() if page_obj else False,
            'number': page_obj.number if page_obj else 1,
            'num_pages': page_obj.paginator.num_pages if page_obj else 1,
            'start_index': page_obj.start_index() if page_obj else 1,
            'end_index': page_obj.end_index() if page_obj else total_count,
        })
    except Exception as e:
        # Return error response
        return JsonResponse({
            'error': str(e),
            'html': '<tr><td colspan="18" class="text-center text-danger">حدث خطأ في تحميل البيانات</td></tr>',
            'total_count': 0
        }, status=500)

@login_required
def employee_detail(request, pk):
    employee = get_object_or_404(Employee, pk=pk)

    # Get performance evaluations (annual reports)
    from performance.models import PerformanceEvaluation
    annual_reports = PerformanceEvaluation.objects.filter(employee=employee).order_by('-year')

    # Get penalties from disciplinary app
    penalties = employee.disciplinary_penalties.all()

    # Get current employment and appointment type
    from employment.models import Employment, AppointmentType
    current_employment = employee.employments.filter(is_current=True).first()
    appointment_types = AppointmentType.objects.all().order_by('name')

    # Initialize forms
    report_form = AnnualReportForm()

    # Handle annual report form submission
    if request.method == 'POST' and 'report_form' in request.POST:
        from performance.models import PerformanceEvaluation
        year = request.POST.get('year')
        score = request.POST.get('score')
        notes = request.POST.get('notes', '')

        if year and score:
            # Check if evaluation already exists for this employee and year
            existing_evaluation = PerformanceEvaluation.objects.filter(employee=employee, year=year).first()
            if existing_evaluation:
                # Update existing evaluation
                existing_evaluation.score = score
                existing_evaluation.comments = notes
                existing_evaluation.save()
                messages.success(request, 'تم تحديث التقرير السنوي بنجاح.')
            else:
                # Create new evaluation
                PerformanceEvaluation.objects.create(
                    employee=employee,
                    year=year,
                    score=score,
                    max_score=100.00,
                    comments=notes
                )
                messages.success(request, 'تم إضافة التقرير السنوي بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
        else:
            messages.error(request, 'الرجاء إدخال السنة والدرجة.')

    # Handle appointment type update
    if request.method == 'POST' and 'appointment_type_form' in request.POST:
        appointment_type_id = request.POST.get('appointment_type')
        if appointment_type_id:
            appointment_type = AppointmentType.objects.get(id=appointment_type_id)
            if current_employment:
                current_employment.appointment_type = appointment_type
                current_employment.save()
                messages.success(request, 'تم تحديث صفة التعيين بنجاح.')
            else:
                # Create new employment if none exists
                from employment.models import EmploymentStatus, Department
                department, _ = Department.objects.get_or_create(
                    name='قسم عام',
                    defaults={'description': 'قسم افتراضي للموظفين الجدد'}
                )
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )
                Employment.objects.create(
                    employee=employee,
                    department=department,
                    status=status,
                    appointment_type=appointment_type,
                    start_date=employee.hire_date,
                    is_current=True
                )
                messages.success(request, 'تم إضافة صفة التعيين بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)

    # Get current year for the report form
    from datetime import date
    current_year = date.today().year

    return render(request, 'employees/employee_detail.html', {
        'employee': employee,
        'annual_reports': annual_reports,
        'penalties': penalties,
        'report_form': report_form,
        'current_employment': current_employment,
        'appointment_types': appointment_types,
        'current_year': current_year
    })

@login_required
def employee_create(request):
    """View for creating a new employee"""
    # Get departments, positions and appointment types for the form
    from employment.models import Department, Position, AppointmentType
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')
    appointment_types = AppointmentType.objects.all().order_by('name')

    if request.method == 'POST':
        form = EmployeeForm(request.POST)

        # Check if ministry number already exists
        ministry_number = request.POST.get('ministry_number')
        if ministry_number and Employee.objects.filter(ministry_number=ministry_number).exists():
            messages.error(request, 'الرقم الوزاري موجود بالفعل. الرجاء استخدام رقم وزاري آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'is_create': True
            })

        # Check if national ID already exists
        national_id = request.POST.get('national_id')
        if national_id and Employee.objects.filter(national_id=national_id).exists():
            messages.error(request, 'الرقم الوطني موجود بالفعل. الرجاء استخدام رقم وطني آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'departments': departments,
                'positions': positions,
                'appointment_types': appointment_types,
                'is_create': True
            })

        if form.is_valid():
            employee = form.save()

            # Handle position if provided
            position_id = request.POST.get('position')
            if position_id:
                from employment.models import Employment, EmploymentStatus, EmployeePosition
                position = Position.objects.get(id=position_id)

                # Get or create a default department
                department, _ = Department.objects.get_or_create(
                    name='قسم عام',
                    defaults={'description': 'قسم افتراضي للموظفين الجدد'}
                )

                # Get or create a default employment status
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )

                # Get appointment type if provided
                appointment_type_id = request.POST.get('appointment_type')
                appointment_type = None
                if appointment_type_id:
                    appointment_type = AppointmentType.objects.get(id=appointment_type_id)

                # Create employment record
                employment = Employment.objects.create(
                    employee=employee,
                    department=department,
                    position=position,
                    status=status,
                    appointment_type=appointment_type,
                    start_date=employee.hire_date,
                    is_current=True
                )

            messages.success(request, 'تم إضافة الموظف بنجاح.')
            return redirect('employees:employee_detail', pk=employee.pk)
    else:
        form = EmployeeForm()
    
    return render(request, 'employees/employee_form.html', {
        'form': form,
        'departments': departments,
        'positions': positions,
        'appointment_types': appointment_types,
        'is_create': True
    })

@login_required
def annual_report_create(request):
    """View for creating a new annual report"""
    # Import PerformanceEvaluation model
    from performance.models import PerformanceEvaluation

    if request.method == 'POST':
        print('\n\nPOST data:', request.POST)
        form = AnnualReportForm(request.POST)
        print('Form is bound:', form.is_bound)
        if form.is_valid():
            try:
                print('Form is valid. Cleaned data:', form.cleaned_data)

                # Get employee from employee_id
                employee_id = form.cleaned_data.get('employee_id')
                ministry_number = form.cleaned_data.get('ministry_number')

                # Find employee
                employee = None
                if employee_id:
                    try:
                        employee = Employee.objects.get(id=employee_id)
                        print(f'Found employee by ID: {employee.id} - {employee.full_name}')
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على الموظف.')
                        return render(request, 'employees/annual_report_form.html', {'form': form})
                elif ministry_number:
                    try:
                        employee = Employee.objects.get(ministry_number=ministry_number)
                        print(f'Found employee by ministry number: {employee.id} - {employee.full_name}')
                    except Employee.DoesNotExist:
                        messages.error(request, 'لم يتم العثور على موظف بهذا الرقم الوزاري')
                        return render(request, 'employees/annual_report_form.html', {'form': form})
                else:
                    messages.error(request, 'الرجاء إدخال الرقم الوزاري للموظف')
                    return render(request, 'employees/annual_report_form.html', {'form': form})

                # Get form data
                year = form.cleaned_data['year']
                score = form.cleaned_data['score']
                notes = form.cleaned_data.get('notes', '')

                print(f'Creating performance evaluation for {employee.full_name}, year: {year}, score: {score}')

                # Check if evaluation already exists for this employee and year
                existing_evaluation = PerformanceEvaluation.objects.filter(employee=employee, year=year).first()
                if existing_evaluation:
                    # Update existing evaluation
                    existing_evaluation.score = score
                    existing_evaluation.comments = notes
                    existing_evaluation.save()
                    print(f'Updated existing evaluation with ID: {existing_evaluation.id}')
                    evaluation = existing_evaluation
                else:
                    # Create new PerformanceEvaluation object
                    evaluation = PerformanceEvaluation(
                        employee=employee,
                        year=year,
                        score=score,
                        max_score=100.00,  # Default max score
                        comments=notes
                    )

                    # Save the evaluation
                    evaluation.save()
                    print('Created new performance evaluation with ID:', evaluation.id)

                messages.success(request, 'تم إضافة التقرير السنوي بنجاح.')
                return redirect('performance:performance_list')

            except Exception as e:
                print('Error saving annual report:', str(e))
                messages.error(request, f'حدث خطأ أثناء حفظ التقرير السنوي: {str(e)}')
        else:
            print('\nForm is invalid. Errors:', form.errors)
    else:
        # Initialize form with default values
        from datetime import date
        form = AnnualReportForm(initial={'year': date.today().year})

    return render(request, 'employees/annual_report_form.html', {'form': form})

@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})

@login_required
def employee_update(request, pk):
    employee = get_object_or_404(Employee, pk=pk)

    # Get departments, positions and appointment types for the form
    from employment.models import Department, Position, AppointmentType
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')
    appointment_types = AppointmentType.objects.all().order_by('name')

    if request.method == 'POST':
        form = EmployeeForm(request.POST, instance=employee)

        # Check if ministry number already exists (but not for this employee)
        ministry_number = request.POST.get('ministry_number')
        if ministry_number and Employee.objects.filter(ministry_number=ministry_number).exclude(id=employee.id).exists():
            messages.error(request, 'الرقم الوزاري موجود بالفعل لموظف آخر. الرجاء استخدام رقم وزاري آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'employee': employee,
                'departments': departments,
                'positions': positions
            })

        # Check if national ID already exists (but not for this employee)
        national_id = request.POST.get('national_id')
        if national_id and Employee.objects.filter(national_id=national_id).exclude(id=employee.id).exists():
            messages.error(request, 'الرقم الوطني موجود بالفعل لموظف آخر. الرجاء استخدام رقم وطني آخر.')
            return render(request, 'employees/employee_form.html', {
                'form': form,
                'employee': employee,
                'departments': departments,
                'positions': positions
            })

        if form.is_valid():
            # الحصول على القسم القديم من جدول التوظيف
            from employment.models import Employment
            old_employment = Employment.objects.filter(employee=employee, is_current=True).first()
            old_department = old_employment.department.name if old_employment and old_employment.department else None

            # Save the form
            updated_employee = form.save()

            # الحصول على القسم الجديد من جدول التوظيف
            new_employment = Employment.objects.filter(employee=updated_employee, is_current=True).first()
            new_department = new_employment.department.name if new_employment and new_employment.department else None

            # Check if department has changed
            if old_department and new_department and old_department != new_department:
                # Create internal transfer record
                InternalTransfer.objects.create(
                    employee=updated_employee,
                    previous_department=old_department,
                    new_department=new_department,
                    transfer_date=timezone.now().date(),
                    start_date=timezone.now().date(),
                    notes=f"تم النقل تلقائياً من {old_department} إلى {new_department}"
                )

            # Handle position if provided
            position_id = request.POST.get('position')
            if position_id:
                from employment.models import Employment, EmploymentStatus, EmployeePosition
                position = Position.objects.get(id=position_id)

                # Get or create a default department
                department, _ = Department.objects.get_or_create(
                    name='قسم عام',
                    defaults={'description': 'قسم افتراضي للموظفين الجدد'}
                )

                # Get or create a default employment status
                status, _ = EmploymentStatus.objects.get_or_create(
                    name='permanent',
                    defaults={'description': 'Permanent employment'}
                )

                # Get appointment type if provided
                appointment_type_id = request.POST.get('appointment_type')
                appointment_type = None
                if appointment_type_id:
                    appointment_type = AppointmentType.objects.get(id=appointment_type_id)

                # Check if employee already has an employment
                current_employment = employee.employments.filter().first()
                if current_employment:
                    # Check if position has changed
                    position_changed = current_employment.position != position

                    # Update existing employment
                    current_employment.position = position
                    current_employment.department = department
                    current_employment.appointment_type = appointment_type
                    current_employment.save()

                    # If position has changed, create a new EmployeePosition record
                    if position_changed:
                        EmployeePosition.objects.create(
                            employee=employee,
                            position=position,
                            date_obtained=timezone.now().date(),
                            notes="تم تحديث المسمى الوظيفي"
                        )
                else:
                    # Create new employment
                    Employment.objects.create(
                        employee=employee,
                        position=position,
                        department=department,
                        status=status,
                        appointment_type=appointment_type,
                        start_date=employee.hire_date,
                        is_current=True
                    )

                    # Create an EmployeePosition record
                    EmployeePosition.objects.create(
                        employee=employee,
                        position=position,
                        date_obtained=employee.hire_date,
                        notes="تم إنشاء المسمى الوظيفي عند تحديث الموظف"
                    )

            messages.success(request, 'تم تحديث بيانات الموظف بنجاح.')
            return redirect('employees:employee_list')
    else:
        form = EmployeeForm(instance=employee)

    # Get current position for the form
    current_position = None
    current_employment = employee.employments.filter().first()
    if current_employment:
        current_position = current_employment.position

    return render(request, 'employees/employee_form.html', {
        'form': form,
        'employee': employee,
        'departments': departments,
        'positions': positions,
        'appointment_types': appointment_types,
        'current_position': current_position
    })

@login_required
def employee_delete(request, pk):
    employee = get_object_or_404(Employee, pk=pk)
    if request.method == 'POST':
        employee_name = employee.full_name  # Store name before deletion
        employee.delete()
        messages.success(request, 'تم حذف الموظف بنجاح.')

        # Create notification for employee deletion
        try:
            from notifications.utils import notify_admins
            notify_admins(
                "تم حذف موظف",
                f"تم حذف الموظف {employee_name} من النظام",
                "warning",
                "fa-user-minus"
            )
        except ImportError:
            pass  # Notifications app not available

        return redirect('employees:employee_list')

    # Get all employees for the list
    search_query = request.GET.get('search', '')
    if search_query:
        employees = Employee.objects.filter(
            Q(ministry_number__icontains=search_query) |
            Q(national_id__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(school__icontains=search_query)
        )
    else:
        employees = Employee.objects.all()
    
    # Apply numeric sorting by ministry number
    from .utils import order_employees_by_ministry_number
    employees = order_employees_by_ministry_number(employees)

    # Get departments and positions for the form
    from employment.models import Department, Position
    departments = Department.objects.all().order_by('name')
    positions = Position.objects.all().order_by('name')

    return render(request, 'employees/employee_data.html', {
        'employee_to_delete': employee,
        'employees': employees,
        'search_query': search_query,
        'is_delete': True,
        'departments': departments,
        'positions': positions
    })

@login_required
def employee_import_export(request):
    if request.method == 'POST' and request.FILES.get('excel_file'):
        form = EmployeeImportForm(request.POST, request.FILES)
        if form.is_valid():
            excel_file = request.FILES['excel_file']
            try:
                # Read the Excel file
                df = pd.read_excel(excel_file)
                print(f"Excel file loaded successfully. Shape: {df.shape}")
                print(f"Columns: {list(df.columns)}")
                
                # Clean up empty rows
                df = df.dropna(how='all')  # Remove completely empty rows
                print(f"After removing empty rows: {df.shape}")

                # Create a mapping for column names (handle different encodings and exact matches)
                column_mapping = {}
                
                # Define expected column names with variations
                column_variations = {
                    'الرقم الوزاري': ['الرقم الوزاري', 'رقم وزاري', 'ministry_number'],
                    'الرقم الوطني': ['الرقم الوطني', 'رقم وطني', 'national_id'],
                    'الاسم الكامل': ['الاسم الكامل', 'اسم كامل', 'الاسم', 'full_name'],
                    'المؤهل العلمي': ['المؤهل العلمي', 'مؤهل علمي', 'المؤهل', 'qualification'],
                    'التخصص': ['التخصص', 'تخصص', 'specialization'],
                    'تاريخ التعيين': ['تاريخ التعيين', 'تاريخ تعيين', 'hire_date'],
                    'القسم': ['القسم', 'قسم', 'المدرسة', 'مدرسة', 'department', 'school'],
                    'تاريخ الميلاد': ['تاريخ الميلاد', 'تاريخ ميلاد', 'birth_date'],
                    'العنوان': ['العنوان', 'عنوان', 'address'],
                    'رقم الهاتف': ['رقم الهاتف', 'هاتف', 'phone_number'],
                    'الجنس': ['الجنس', 'جنس', 'gender']
                }
                
                # Map actual columns to logical names
                for logical_name, variations in column_variations.items():
                    for col in df.columns:
                        col_str = str(col).strip()
                        if col_str in variations:
                            column_mapping[logical_name] = col
                            break
                        # Try partial matching for Arabic columns
                        for variation in variations:
                            if variation in col_str:
                                column_mapping[logical_name] = col
                                break
                        if logical_name in column_mapping:
                            break

                # Check if required columns exist
                if 'الرقم الوزاري' not in column_mapping and 'الاسم الكامل' not in column_mapping:
                    available_cols = ', '.join([str(col) for col in df.columns])
                    messages.error(request, f'لم يتم العثور على الأعمدة المطلوبة. الأعمدة المتاحة: {available_cols}')
                    return render(request, 'employees/employee_import_export.html', context)
                
                print(f"Column mapping: {column_mapping}")

                # Keep track of imported and skipped employees
                imported_count = 0
                skipped_count = 0
                skipped_employees = []

                # Process each row
                total_rows = len(df)
                print(f"Starting to process {total_rows} rows...")
                
                for index, row in df.iterrows():
                    try:
                        # Print progress every 10 rows
                        if (index + 1) % 10 == 0 or index == 0:
                            print(f"Processing row {index + 2} of {total_rows + 1}...")
                        
                        # Helper function to get column value safely
                        def get_column_value(logical_name, default=''):
                            if logical_name in column_mapping:
                                actual_col = column_mapping[logical_name]
                                value = row.get(actual_col, default)
                                if pd.isna(value) or str(value).strip() == 'nan':
                                    return default
                                return str(value).strip()
                            return default

                        # Clean up the data and handle NaN values
                        ministry_number = get_column_value('الرقم الوزاري')
                        if not ministry_number:
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: الرقم الوزاري غير موجود")
                            continue

                        if Employee.objects.filter(ministry_number=ministry_number).exists():
                            skipped_count += 1
                            skipped_employees.append(f"الرقم الوزاري {ministry_number}: موجود بالفعل")
                            continue

                        # Handle national ID
                        national_id = get_column_value('الرقم الوطني')
                        if national_id and Employee.objects.filter(national_id=national_id).exists():
                            skipped_count += 1
                            skipped_employees.append(f"الرقم الوطني {national_id}: موجود بالفعل")
                            continue

                        # Handle gender
                        gender_value = get_column_value('الجنس')
                        gender = 'male' if gender_value == 'ذكر' else 'female' if gender_value == 'انثى' else None

                        # Handle dates with proper error handling
                        def get_date_value(logical_name):
                            if logical_name in column_mapping:
                                actual_col = column_mapping[logical_name]
                                date_value = row.get(actual_col)
                                
                                # If date is empty or NaN, return None
                                if pd.isna(date_value) or date_value == '' or str(date_value).strip() == '':
                                    return None
                                
                                # If it's already a datetime object
                                if hasattr(date_value, 'date'):
                                    return date_value.date()
                                elif hasattr(date_value, 'strftime'):  # Handle pandas Timestamp
                                    return date_value.date()
                                
                                # If it's a string, try different formats
                                elif isinstance(date_value, str):
                                    date_str = date_value.strip()
                                    if not date_str:
                                        return None
                                    
                                    from datetime import datetime
                                    # Try different date formats
                                    formats = ['%Y-%m-%d', '%d/%m/%Y', '%m/%d/%Y', '%Y/%m/%d', '%d-%m-%Y']
                                    for fmt in formats:
                                        try:
                                            return datetime.strptime(date_str, fmt).date()
                                        except ValueError:
                                            continue
                                    return None
                                else:
                                    return None
                            return None

                        hire_date = get_date_value('تاريخ التعيين')
                        birth_date = get_date_value('تاريخ الميلاد')
                        
                        # Validate that required dates are present
                        if not hire_date:
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: تاريخ التعيين مطلوب")
                            continue
                            
                        if not birth_date:
                            skipped_count += 1  
                            skipped_employees.append(f"صف {index + 2}: تاريخ الميلاد مطلوب")
                            continue

                        # Handle other required fields
                        full_name = get_column_value('الاسم الكامل')
                        if not full_name:
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: الاسم الكامل مطلوب")
                            continue
                            
                        qualification = get_column_value('المؤهل العلمي')
                        if not qualification:
                            qualification = 'غير محدد'
                            
                        specialization = get_column_value('التخصص')
                        if not specialization:
                            specialization = 'غير محدد'
                            
                        school = get_column_value('القسم')
                        if not school:
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: القسم مطلوب")
                            continue
                            
                        address = get_column_value('العنوان')
                        if not address:
                            address = 'غير محدد'
                            
                        phone_number = get_column_value('رقم الهاتف')
                        if not phone_number:
                            phone_number = ''

                        # Final validation before creating employee
                        if not all([ministry_number, full_name, hire_date, birth_date, school]):
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: بيانات ناقصة (الرقم الوزاري، الاسم، تاريخ التعيين، تاريخ الميلاد، القسم مطلوبة)")
                            continue

                        # Create new employee
                        employee = Employee(
                            ministry_number=ministry_number,
                            national_id=national_id or '',
                            full_name=full_name,
                            qualification=qualification,
                            specialization=specialization,
                            hire_date=hire_date,
                            school=school,
                            birth_date=birth_date,
                            address=address,
                            phone_number=phone_number,
                            gender=gender or 'male'  # Default to male if not specified
                        )
                        
                        # Validate the employee instance before saving
                        try:
                            employee.full_clean()  # This will run model validation
                            employee.save()
                            print(f"Successfully created employee: {employee.full_name} with ministry number: {employee.ministry_number}")
                            imported_count += 1
                        except Exception as validation_error:
                            skipped_count += 1
                            skipped_employees.append(f"صف {index + 2}: خطأ في التحقق من البيانات - {str(validation_error)}")
                            continue
                    except Exception as e:
                        # Log the error and continue with the next row
                        error_msg = f"خطأ في صف {index + 2}: {str(e)}"
                        print(error_msg)
                        skipped_count += 1
                        skipped_employees.append(error_msg)
                        continue

                # Show success message with details
                total_processed = imported_count + skipped_count
                print(f"Processing completed. Total rows: {len(df)}, Imported: {imported_count}, Skipped: {skipped_count}")
                
                if imported_count > 0:
                    messages.success(request, f'✅ تم استيراد {imported_count} موظف بنجاح من أصل {total_processed} سجل في الملف.')

                # Show detailed warning for skipped employees
                if skipped_count > 0:
                    skipped_message = f'⚠️ تم تخطي {skipped_count} سجل.'
                    if len(skipped_employees) > 0:
                        # Group errors by type for better readability
                        error_types = {}
                        for error in skipped_employees[:10]:  # Show first 10 errors
                            if 'تاريخ التعيين مطلوب' in error:
                                error_types.setdefault('تاريخ التعيين مفقود', []).append(error.split(':')[0])
                            elif 'تاريخ الميلاد مطلوب' in error:
                                error_types.setdefault('تاريخ الميلاد مفقود', []).append(error.split(':')[0])
                            elif 'الاسم الكامل مطلوب' in error:
                                error_types.setdefault('الاسم الكامل مفقود', []).append(error.split(':')[0])
                            elif 'القسم مطلوب' in error:
                                error_types.setdefault('القسم مفقود', []).append(error.split(':')[0])
                            elif 'موجود بالفعل' in error:
                                error_types.setdefault('بيانات مكررة', []).append(error)
                            else:
                                error_types.setdefault('أخطاء أخرى', []).append(error)
                        
                        if error_types:
                            skipped_message += '\n\nملخص الأخطاء:'
                            for error_type, errors in error_types.items():
                                skipped_message += f'\n• {error_type}: {len(errors)} سجل'
                                if len(errors) <= 3:
                                    for err in errors:
                                        if 'صف' in str(err):
                                            skipped_message += f'\n  - {err}'
                        
                        if len(skipped_employees) > 10:
                            skipped_message += f'\n... و{len(skipped_employees) - 10} خطأ آخر'
                    
                    skipped_message += '\n\n💡 تأكد من:\n• وجود جميع الحقول المطلوبة\n• صحة تنسيق التواريخ (YYYY-MM-DD)\n• عدم تكرار الأرقام الوزارية والوطنية'
                    messages.warning(request, skipped_message)
                
                if imported_count == 0 and skipped_count == 0:
                    messages.warning(request, '⚠️ لم يتم العثور على بيانات صالحة للاستيراد في الملف. تأكد من صحة تنسيق الملف والأعمدة.')
                elif imported_count == 0 and skipped_count > 0:
                    messages.error(request, f'❌ لم يتم استيراد أي موظف. جميع السجلات البالغ عددها {skipped_count} تحتوي على أخطاء.')
                
                return redirect('employees:employee_import_export')
            except Exception as e:
                messages.error(request, f'حدث خطأ أثناء استيراد البيانات: {str(e)}')
        else:
            messages.error(request, 'الرجاء تصحيح الأخطاء أدناه.')
    else:
        form = EmployeeImportForm()

    # Get employee statistics
    employees = Employee.objects.all()
    # Apply numeric sorting by ministry number
    from .utils import order_employees_by_ministry_number
    employees_ordered = order_employees_by_ministry_number(employees)
    
    # Check if the result is a list or queryset
    if isinstance(employees_ordered, list):
        employees_count = len(employees_ordered)
        # For filtering, we need to work with the original queryset
        male_count = employees.filter(gender='male').count()
        female_count = employees.filter(gender='female').count()
        # الحصول على عدد الأقسام من جدول التوظيف
        from employment.models import Employment
        departments_count = Employment.objects.filter(
            employee__in=[e.id for e in employees_ordered],
            is_current=True
        ).values('department').distinct().count()
    else:
        employees_count = employees_ordered.count()
        male_count = employees_ordered.filter(gender='male').count()
        female_count = employees_ordered.filter(gender='female').count()
        # الحصول على عدد الأقسام من جدول التوظيف
        from employment.models import Employment
        departments_count = Employment.objects.filter(
            employee__in=employees_ordered,
            is_current=True
        ).values('department').distinct().count()

    context = {
        'form': form,
        'employees_count': employees_count,
        'male_count': male_count,
        'female_count': female_count,
        'departments_count': departments_count,
    }

    if request.GET.get('export') == 'excel':
        # Export to Excel - Only active employees (exclude retired, transferred, and service purchase)
        retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
        transferred_employee_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
        
        # Also exclude employees with active service purchase
        try:
            from employment.models import ServicePurchase
            service_purchase_employee_ids = ServicePurchase.objects.filter(is_active=True).values_list('employee_id', flat=True)
        except ImportError:
            service_purchase_employee_ids = []
        
        # Combine all exclusions
        excluded_ids = list(retired_employee_ids) + list(transferred_employee_ids) + list(service_purchase_employee_ids)
        employees_for_export = Employee.objects.exclude(id__in=excluded_ids)
        
        # Apply numeric sorting by ministry number
        from .utils import order_employees_by_ministry_number
        employees_for_export = order_employees_by_ministry_number(employees_for_export)
        
        # Ensure we have a list for iteration
        if not isinstance(employees_for_export, list):
            employees_for_export = list(employees_for_export)
            
        # تصدير البيانات الأساسية فقط (الحقول الموجودة في جدول الموظفين)
        data = {
            'الرقم الوزاري': [e.ministry_number or '' for e in employees_for_export],
            'الرقم الوطني': [e.national_id or '' for e in employees_for_export],
            'الاسم الكامل': [e.full_name or '' for e in employees_for_export],
            'الجنس': ['ذكر' if e.gender == 'male' else 'أنثى' if e.gender == 'female' else '' for e in employees_for_export],
            'تاريخ الميلاد': [e.birth_date.strftime('%Y-%m-%d') if e.birth_date else '' for e in employees_for_export],
            'المؤهل العلمي': [e.qualification or '' for e in employees_for_export],
            'دبلوم عالي': [e.post_graduate_diploma or '' for e in employees_for_export],
            'ماجستير': [e.masters_degree or '' for e in employees_for_export],
            'دكتوراه': [e.phd_degree or '' for e in employees_for_export],
            'التخصص': [e.specialization or '' for e in employees_for_export],
            'تاريخ التعيين': [e.hire_date.strftime('%Y-%m-%d') if e.hire_date else '' for e in employees_for_export],
            'القسم الحالي': [get_employee_department(e) for e in employees_for_export],
            'العنوان': [e.address or '' for e in employees_for_export],
            'رقم الهاتف': [e.phone_number or '' for e in employees_for_export],
        }
        df = pd.DataFrame(data)

        # Create a response with Excel file
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='الموظفين')

            # Get the worksheet
            workbook = writer.book
            worksheet = writer.sheets['الموظفين']

            # Format the worksheet for Arabic
            worksheet.sheet_view.rightToLeft = True

            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = (max_length + 2) * 1.2
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # Add formatting
            header_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color='E0E0E0', end_color='E0E0E0', fill_type='solid')
            border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Apply formatting to header row
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.border = border

            # Apply borders to all cells
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.border = border

        output.seek(0)
        response = HttpResponse(output.read(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=employees.xlsx'
        return response

    # PDF export option (استخدام النظام المبسط)
    if request.GET.get('export') == 'pdf':
        from .pdf_export_simple import export_employees_pdf_simple
        return export_employees_pdf_simple(request)
    
    # PDF download option
    if request.GET.get('export') == 'pdf_download':
        from .pdf_export_simple import export_employees_pdf_download
        return export_employees_pdf_download(request)
    
    # Enhanced Excel export option (with all fields)
    if request.GET.get('export') == 'excel' and request.GET.get('include_all') == 'true':
        from .import_export_simple import export_employees_excel_enhanced
        return export_employees_excel_enhanced(request)
    
    # Download basic template option
    if request.GET.get('download_template') == 'true':
        from .template_basic import create_basic_employee_template
        template_file = create_basic_employee_template()
        response = HttpResponse(
            template_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="قالب_استيراد_الموظفين_أساسي.xlsx"'
        return response
    
    # Download enhanced template option
    if request.GET.get('download_template') == 'enhanced':
        from .import_export_simple import create_employee_template_enhanced
        template_file = create_employee_template_enhanced()
        response = HttpResponse(
            template_file.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename="قالب_استيراد_الموظفين_شامل.xlsx"'
        return response

    return render(request, 'employees/employee_import_export.html', context)


@login_required
def export_employees_pdf(request):
    """View for exporting employee data as PDF with proper Arabic support using arabic_reshaper and bidi"""
    # Get only active employees (exclude retired, transferred, and service purchase)
    retired_employee_ids = RetiredEmployee.objects.values_list('employee_id', flat=True)
    transferred_employee_ids = ExternalTransfer.objects.values_list('employee_id', flat=True)
    
    # Also exclude employees with active service purchase
    try:
        from employment.models import ServicePurchase
        service_purchase_employee_ids = ServicePurchase.objects.filter(is_active=True).values_list('employee_id', flat=True)
    except ImportError:
        service_purchase_employee_ids = []
    
    # Combine all exclusions
    excluded_ids = list(retired_employee_ids) + list(transferred_employee_ids) + list(service_purchase_employee_ids)
    employees = Employee.objects.exclude(id__in=excluded_ids).order_by('ministry_number')

    # Create a response object with PDF content type
    response = HttpResponse(content_type='application/pdf; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="employees_data.pdf"'

    # Register Arabic fonts - we'll register multiple fonts for better compatibility
    fonts_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'static', 'fonts')

    # Register Noto Sans Arabic font
    noto_sans_path = os.path.join(fonts_dir, 'NotoSansArabic-Regular.ttf')
    pdfmetrics.registerFont(TTFont('NotoSansArabic', noto_sans_path))

    # Register Arial font as fallback
    arial_path = os.path.join(fonts_dir, 'arial.ttf')
    if os.path.exists(arial_path):
        pdfmetrics.registerFont(TTFont('Arial', arial_path))

    # Create the PDF document with RTL support and landscape orientation for wider table
    doc = SimpleDocTemplate(
        response,
        pagesize=landscape(A4),  # Use landscape orientation for wider table
        rightMargin=10,  # Further reduced margins to maximize content space
        leftMargin=10,
        topMargin=10,
        bottomMargin=30,  # Further reduced bottom margin but still enough for the date
        encoding='UTF-8'  # Explicitly set UTF-8 encoding
    )

    # Create styles
    styles = getSampleStyleSheet()

    # Create a custom style for Arabic text with improved settings
    arabic_style = ParagraphStyle(
        'ArabicStyle',
        parent=styles['Normal'],
        fontName='NotoSansArabic',
        fontSize=8,  # Increased font size by one degree
        alignment=1,  # Center alignment
        leading=9,  # Increased line height
        spaceAfter=0,  # No space after
        firstLineIndent=0,
        rightIndent=0,
        leftIndent=0,
        wordWrap='LTR',  # Force left-to-right to prevent wrapping
        splitLongWords=0  # Prevent splitting long words
    )

    arabic_header_style = ParagraphStyle(
        'ArabicHeaderStyle',
        parent=styles['Heading1'],
        fontName='NotoSansArabic',
        fontSize=18,
        alignment=1,  # Center alignment
        leading=24,
        spaceAfter=12,
        textColor=colors.darkblue
    )

    # Define light beige color for table headers (light sandy color)
    light_beige = colors.Color(0.94, 0.90, 0.80)

    # Helper function to process Arabic text
    def process_arabic_text(text, max_length=None):
        if not text:
            return ""
        # Convert to string
        text_str = str(text)

        # Special handling for dates to ensure they display correctly
        if '-' in text_str and len(text_str) == 10:  # This is likely a date in format dd-mm-yyyy
            # For dates, we want to preserve the format exactly as is
            parts = text_str.split('-')
            if len(parts) == 3:
                # Reshape each part separately to prevent reshaping issues with numbers
                if ARABIC_SUPPORT:
                    day = arabic_reshaper.reshape(parts[0])
                    month = arabic_reshaper.reshape(parts[1])
                    year = arabic_reshaper.reshape(parts[2])
                else:
                    day = parts[0]
                    month = parts[1]
                    year = parts[2]
                # Combine with explicit RTL markers to ensure correct display
                return get_display(day + '-' + month + '-' + year)

        # Truncate if max_length is specified and text is longer
        if max_length and len(text_str) > max_length:
            text_str = text_str[:max_length]

        # Reshape Arabic text
        if ARABIC_SUPPORT:
            reshaped_text = arabic_reshaper.reshape(text_str)
            # Apply bidirectional algorithm
            bidi_text = get_display(reshaped_text)
            return bidi_text
        else:
            return text_str

    # Create the content elements
    elements = []

    # Add title
    title_text = process_arabic_text('بيانات الموظفين')
    title = Paragraph(title_text, arabic_header_style)
    elements.append(title)
    elements.append(Spacer(1, 20))

    # Get the data from the table in the UI
    # Prepare table headers with processed Arabic text - in RTL order
    headers = [
        process_arabic_text('العنوان'),
        process_arabic_text('تاريخ الميلاد'),
        process_arabic_text('الجنس'),
        process_arabic_text('رقم الهاتف'),
        process_arabic_text('تاريخ التعيين'),
        process_arabic_text('التخصص'),
        process_arabic_text('المؤهل العلمي'),
        process_arabic_text('الرقم الوطني'),
        process_arabic_text('اسم الموظف'),
        process_arabic_text('الرقم الوزاري'),
    ]

    # Create table data with processed headers
    data = [[Paragraph(header, arabic_style) for header in headers]]

    # Add data rows with processed Arabic text
    for employee in employees:
        # Format birth date with clear separators
        birth_date = ""
        if employee.birth_date:
            birth_date = employee.birth_date.strftime("%d-%m-%Y")  # Using dashes for better readability

        # Format hire date with clear separators
        hire_date = ""
        if employee.hire_date:
            hire_date = employee.hire_date.strftime("%d-%m-%Y")  # Using dashes for better readability

        # Format gender
        gender = ""
        if employee.gender == 'male':
            gender = "ذكر"
        elif employee.gender == 'female':
            gender = "أنثى"

        # Process each text field - don't limit qualification to show full content
        ministry_number = process_arabic_text(employee.ministry_number, max_length=15)
        full_name = process_arabic_text(employee.full_name, max_length=30)
        national_id = process_arabic_text(employee.national_id or "", max_length=15)
        qualification = process_arabic_text(employee.qualification or "")  # No limit to show full content
        specialization = process_arabic_text(employee.specialization or "", max_length=18)  # Reduced specialization length
        hire_date_processed = process_arabic_text(hire_date, max_length=10)  # Ensure date is fixed length
        phone_number = process_arabic_text(employee.phone_number or "", max_length=15)  # Ensure phone number is fixed length
        gender_processed = process_arabic_text(gender, max_length=5)
        birth_date_processed = process_arabic_text(birth_date, max_length=10)  # Ensure date is fixed length
        address = process_arabic_text(employee.address or "", max_length=25)  # Reduced address length

        # Create special style for date fields to ensure they don't wrap
        date_style = ParagraphStyle(
            'DateStyle',
            parent=arabic_style,
            fontName='NotoSansArabic',
            fontSize=8,  # Increased font size
            alignment=1,
            wordWrap=None,
            splitLongWords=0
        )

        # Create special style for qualification field - allow wrapping to show full content
        qualification_style = ParagraphStyle(
            'QualificationStyle',
            parent=arabic_style,
            fontName='NotoSansArabic',
            fontSize=8,  # Keep font size
            alignment=1,
            leading=10,  # Increased line height for wrapped text
            wordWrap='CJK',  # Allow wrapping to show full content
            splitLongWords=1  # Allow splitting long words if needed
        )

        # Add row in RTL order - using special styles for different fields
        row = [
            Paragraph(address, arabic_style),
            Paragraph(birth_date_processed, date_style),  # Use date style
            Paragraph(gender_processed, arabic_style),
            Paragraph(phone_number, arabic_style),
            Paragraph(hire_date_processed, date_style),  # Use date style
            Paragraph(specialization, arabic_style),
            Paragraph(qualification, qualification_style),  # Use qualification style to allow wrapping
            Paragraph(national_id, arabic_style),
            Paragraph(full_name, arabic_style),
            Paragraph(ministry_number, arabic_style),
        ]
        data.append(row)

    # Create the table with optimized column widths for single-line display in landscape mode
    # Wider columns for text that might be longer, taking advantage of landscape orientation
    # Adjusted column widths to better fit the content - further reducing address and specialization, further increasing qualification
    col_widths = [90, 60, 35, 60, 60, 90, 160, 65, 130, 55]  # Further reduced address and specialization, further increased qualification
    table = Table(data, repeatRows=1, colWidths=col_widths)

    # Style the table with improved settings for Arabic
    table_style = TableStyle([
        # Header row styling - changed to light beige color
        ('BACKGROUND', (0, 0), (-1, 0), light_beige),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),  # Changed to black for better readability on light background
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ('FONTNAME', (0, 0), (-1, 0), 'NotoSansArabic'),
        ('FONTSIZE', (0, 0), (-1, 0), 9),  # Keep header font size
        ('BOTTOMPADDING', (0, 0), (-1, 0), 3),  # Keep padding
        ('TOPPADDING', (0, 0), (-1, 0), 3),  # Keep padding

        # Data rows styling
        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
        ('FONTNAME', (0, 1), (-1, -1), 'NotoSansArabic'),
        ('FONTSIZE', (0, 1), (-1, -1), 8),  # Keep font size for data rows
        ('BOTTOMPADDING', (0, 1), (-1, -1), 4),  # Increased padding for wrapped text
        ('TOPPADDING', (0, 1), (-1, -1), 4),  # Increased padding for wrapped text

        # Grid styling
        ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ('BOX', (0, 0), (-1, -1), 2, colors.black),
        ('LINEBELOW', (0, 0), (-1, 0), 2, colors.black),

        # Padding - reduced to maximize space
        ('RIGHTPADDING', (0, 0), (-1, -1), 8),
        ('LEFTPADDING', (0, 0), (-1, -1), 8),
    ])

    # Add zebra striping for better readability
    for i in range(1, len(data)):
        if i % 2 == 0:
            table_style.add('BACKGROUND', (0, i), (-1, i), colors.lightgrey)

    table.setStyle(table_style)

    # Add the table to the elements
    elements.append(table)

    # Add footer with page numbers and date
    def add_page_number_and_date(canvas, doc):
        canvas.saveState()
        canvas.setFont('NotoSansArabic', 10)

        # Add page number
        page_num = canvas.getPageNumber()
        page_text = process_arabic_text(f"صفحة {page_num}")
        canvas.drawRightString(540, 30, page_text)

        # Add current date at the bottom of the page
        today = date.today()
        formatted_date = f"{today.day}/{today.month}/{today.year}"
        date_text = process_arabic_text(f"تاريخ التقرير: {formatted_date}")
        canvas.drawCentredString(doc.pagesize[0]/2, 30, date_text)

        canvas.restoreState()

    # Build the PDF document with page numbers and date
    doc.build(elements, onFirstPage=add_page_number_and_date, onLaterPages=add_page_number_and_date)

    return response


@login_required
def employee_search_api(request):
    """API endpoint for searching employees by ministry number"""
    ministry_number = request.GET.get('ministry_number', '').strip()

    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)

        # Get current employment info from Employment model
        from employment.models import Employment
        current_employment = Employment.objects.filter(
            employee=employee,
            is_current=True
        ).select_related('department', 'position').first()

        # Get department name
        department_name = 'غير محدد'
        try:
            if current_employment and current_employment.department:
                department_name = current_employment.department.name
            elif employee.school:
                # Handle both string and object cases for school
                if hasattr(employee.school, 'name'):
                    department_name = employee.school.name
                else:
                    department_name = str(employee.school)
        except Exception:
            # If there's any error getting department, use default
            department_name = 'غير محدد'

        # Get position name
        position_name = 'غير محدد'
        try:
            if current_employment and current_employment.position:
                position_name = current_employment.position.name
            else:
                # Try to get latest position from EmployeePosition
                latest_position = employee.positions.order_by('-date_obtained').first()
                if latest_position and latest_position.position:
                    position_name = latest_position.position.name
        except Exception:
            # If there's any error getting position, use default
            position_name = 'غير محدد'

        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number,
                'department': department_name,
                'position': position_name
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': f'حدث خطأ في النظام: {str(e)}'})


@login_required
def get_employee_by_ministry_number(request):
    """AJAX view to get employee details by ministry number"""
    ministry_number = request.GET.get('ministry_number', '')
    if not ministry_number:
        return JsonResponse({'success': False, 'error': 'الرجاء إدخال الرقم الوزاري'})

    try:
        employee = Employee.objects.get(ministry_number=ministry_number)
        return JsonResponse({
            'success': True,
            'employee': {
                'id': employee.id,
                'full_name': employee.full_name,
                'ministry_number': employee.ministry_number
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'لم يتم العثور على موظف بهذا الرقم الوزاري'})
