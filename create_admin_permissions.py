#!/usr/bin/env python
"""
سكريبت لإنشاء صلاحيات كاملة للمستخدم الرئيسي admin
"""

import os
import sys
import django

# إعداد Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hr_system.settings')
django.setup()

from accounts.models import User, UserPermission
from accounts.views import ensure_full_admin_permissions

def create_admin_permissions():
    """إنشاء صلاحيات كاملة للمستخدم admin"""
    
    try:
        # البحث عن المستخدم admin
        admin_user = User.objects.get(username='admin')
        print(f"✅ تم العثور على المستخدم: {admin_user.username}")
        
        # تحديث حالة المستخدم ليصبح مدير كامل
        admin_user.is_admin = True
        admin_user.is_full_admin = True
        admin_user.is_staff = True
        admin_user.is_superuser = True
        admin_user.save()
        print(f"✅ تم تحديث حالة المستخدم: مدير كامل + superuser")
        
        # حذف جميع الصلاحيات الموجودة
        UserPermission.objects.filter(user=admin_user).delete()
        print(f"✅ تم حذف الصلاحيات القديمة")
        
        # إنشاء صلاحيات كاملة لجميع الوحدات
        modules = [
            'accounts',      # إدارة المستخدمين
            'employees',     # الموظفين
            'employment',    # التوظيف
            'disciplinary',  # الانضباط
            'leaves',        # الإجازات
            'performance',   # الأداء
            'ranks',         # الرتب
            'reports',       # التقارير
            'backup',        # النسخ الاحتياطي
            'system_logs',   # سجلات النظام
            'file_management', # إدارة الملفات
            'notifications', # الإشعارات
            'announcements', # الإعلانات
            'home'          # الصفحة الرئيسية
        ]
        
        # الصفحات المرئية لكل وحدة
        module_pages = {
            'accounts': [
                'accounts:user_list',
                'accounts:user_create',
                'accounts:user_detail',
                'accounts:user_update',
                'accounts:user_delete',
                'accounts:user_permissions',
                'accounts:bulk_permissions',
                'accounts:login',
                'accounts:logout'
            ],
            'employees': [
                'employees:employee_list',
                'employees:employee_create',
                'employees:employee_detail',
                'employees:employee_update',
                'employees:employee_delete',
                'employees:employee_search',
                'employees:employee_export'
            ],
            'employment': [
                'employment:department_list',
                'employment:position_list',
                'employment:employment_create',
                'employment:employment_update'
            ],
            'disciplinary': [
                'disciplinary:penalty_list',
                'disciplinary:penalty_create',
                'disciplinary:penalty_update',
                'disciplinary:penalty_delete'
            ],
            'leaves': [
                'leaves:leave_list',
                'leaves:leave_create',
                'leaves:leave_update',
                'leaves:leave_delete',
                'leaves:leave_approve'
            ],
            'performance': [
                'performance:evaluation_list',
                'performance:evaluation_create',
                'performance:evaluation_update'
            ],
            'ranks': [
                'ranks:rank_list',
                'ranks:rank_create',
                'ranks:rank_update'
            ],
            'reports': [
                'reports:report_list',
                'reports:report_create',
                'reports:report_view'
            ],
            'backup': [
                'backup:backup_list',
                'backup:backup_create',
                'backup:backup_restore'
            ],
            'system_logs': [
                'system_logs:log_list',
                'system_logs:log_view'
            ],
            'file_management': [
                'file_management:file_list',
                'file_management:file_upload',
                'file_management:file_download'
            ],
            'notifications': [
                'notifications:notification_list',
                'notifications:notification_create'
            ],
            'announcements': [
                'announcements:announcements_list',
                'announcements:announcement_create',
                'announcements:announcement_update',
                'announcements:announcement_delete'
            ],
            'home': [
                'home:dashboard',
                'home:analytics',
                'home:settings'
            ]
        }
        
        # إنشاء صلاحيات لكل وحدة
        for module in modules:
            pages = module_pages.get(module, [])
            visible_pages_str = ','.join(pages)
            
            permission = UserPermission.objects.create(
                user=admin_user,
                module_name=module,
                can_view=True,
                can_add=True,
                can_edit=True,
                can_delete=True,
                visible_pages=visible_pages_str
            )
            
            print(f"✅ تم إنشاء صلاحيات الوحدة: {module} ({len(pages)} صفحة)")
        
        # استخدام الدالة المدمجة لضمان الصلاحيات الكاملة
        ensure_full_admin_permissions(admin_user)
        
        print(f"\n🎉 تم إنشاء جميع الصلاحيات بنجاح!")
        print(f"👤 المستخدم: {admin_user.username}")
        print(f"🔑 كلمة المرور: admin123")
        print(f"🛡️ الصلاحيات: مدير كامل (Full Admin)")
        print(f"📊 عدد الوحدات: {len(modules)}")
        print(f"📄 إجمالي الصفحات: {sum(len(pages) for pages in module_pages.values())}")
        
        return True
        
    except User.DoesNotExist:
        print("❌ خطأ: لم يتم العثور على المستخدم admin")
        print("💡 تأكد من إنشاء المستخدم أولاً باستخدام: python manage.py createsuperuser")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصلاحيات: {str(e)}")
        return False

if __name__ == "__main__":
    create_admin_permissions()
